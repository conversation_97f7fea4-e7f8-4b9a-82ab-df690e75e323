# Telegram Ingester Authentication for Kubernetes

The Telegram ingester requires authentication with Telegram's API, which normally involves interactive input for verification codes. This document explains how to handle authentication in Kubernetes environments.

## Problem

The telegram ingester needs to authenticate with Telegram, which involves:
1. Requesting a verification code sent to your Telegram app
2. Entering that code interactively
3. Optionally entering a 2FA password

This doesn't work in Kubernetes pods where there's no interactive terminal.

## Solutions

### Option 1: Pre-authenticate with Session File (Recommended)

This is the most reliable approach for production deployments.

#### Step 1: Authenticate Locally

1. **Configure your API credentials** in `apps/telegram-ingester/Config.toml`:
   ```toml
   [telegram]
   api_id = 12345678  # Your API ID from my.telegram.org
   api_hash = "your_api_hash_here"
   phone_number = "+1234567890"
   session_file = "session.dat"
   ```

2. **Run the authentication script**:
   ```bash
   ./scripts/setup-telegram-auth.sh
   ```

   This script will:
   - Run the ingester locally to authenticate
   - Prompt you to enter the verification code
   - Save the session file
   - Create a Kubernetes secret with the session

3. **Deploy to Kubernetes**:
   ```bash
   make k3d-deploy
   ```

#### Step 2: Manual Setup (Alternative)

If you prefer manual setup:

1. **Authenticate locally**:
   ```bash
   cd apps/telegram-ingester
   cargo run
   # Enter verification code when prompted
   ```

2. **Create Kubernetes secret**:
   ```bash
   kubectl create secret generic telegram-session --from-file=session.dat=./session.dat
   ```

3. **Deploy the ingester**:
   ```bash
   helm upgrade --install warda helm/warda \
     --set telegramIngester.enabled=true \
     --set telegramIngester.secrets.apiId="12345678" \
     --set telegramIngester.secrets.apiHash="your_api_hash" \
     --set telegramIngester.secrets.phoneNumber="+1234567890"
   ```

### Option 2: Environment Variable Authentication

For development or CI/CD scenarios where you can provide the verification code programmatically.

1. **Set the verification code** as an environment variable or Kubernetes secret:
   ```bash
   kubectl create secret generic telegram-ingester-secret \
     --from-literal=verification-code="123456"
   ```

2. **Deploy with verification code**:
   ```bash
   helm upgrade --install warda helm/warda \
     --set telegramIngester.enabled=true \
     --set telegramIngester.secrets.verificationCode="123456"
   ```

The ingester will use the environment variable instead of prompting for input.

### Option 3: Copy Session from Another Environment

If you have an authenticated session from another environment:

1. **Copy the session file** from your development environment
2. **Create the secret**:
   ```bash
   kubectl create secret generic telegram-session --from-file=session.dat=/path/to/session.dat
   ```

## How It Works

### Session Persistence

- The ingester stores authentication data in a session file (`session.dat`)
- Once authenticated, future runs use the saved session
- Sessions are long-lived and rarely need re-authentication
- The session file is mounted from a Kubernetes secret at `/data/session.dat`

### Fallback Authentication

The ingester supports multiple authentication methods in order of preference:

1. **Existing session file** - If `/data/session.dat` exists and is valid
2. **Environment variable** - If `TELEGRAM_VERIFICATION_CODE` is set
3. **Interactive input** - Prompts for verification code (won't work in K8s)

### Configuration Override

The Kubernetes deployment overrides configuration via environment variables:

- `TELEGRAM_API_ID` - From `telegram-ingester-secret`
- `TELEGRAM_API_HASH` - From `telegram-ingester-secret`  
- `TELEGRAM_PHONE_NUMBER` - From `telegram-ingester-secret`
- `TELEGRAM_VERIFICATION_CODE` - From `telegram-ingester-secret` (optional)

## Troubleshooting

### Session Expired

If you see authentication errors:

1. **Delete the old session**:
   ```bash
   kubectl delete secret telegram-session
   ```

2. **Re-authenticate locally** and create a new session:
   ```bash
   ./scripts/setup-telegram-auth.sh
   ```

### Verification Code Issues

- Verification codes expire quickly (usually 5 minutes)
- Make sure to use the code immediately after receiving it
- For Option 2, set the verification code right after requesting it

### Pod Startup Issues

Check the logs for authentication errors:
```bash
kubectl logs -l app=telegram-ingester
```

Common issues:
- Missing API credentials
- Invalid session file
- Network connectivity to Telegram servers

## Security Considerations

- **Never commit session files** to version control
- **Use Kubernetes secrets** for sensitive data
- **Rotate API credentials** periodically
- **Monitor for unauthorized access** to your Telegram account

## Production Recommendations

1. **Use Option 1** (session file) for production deployments
2. **Backup your session file** securely
3. **Monitor ingester health** and re-authenticate if needed
4. **Use separate Telegram accounts** for different environments
5. **Implement proper secret management** (e.g., External Secrets Operator)
