# GitHub Secrets Setup for Telegram Ingester

This guide explains how to configure GitHub Secrets for the Telegram ingester deployment via GitHub Actions.

## Overview

The Telegram ingester is configured through GitHub Secrets to keep sensitive credentials secure. When you push to the `dev` branch, the GitHub Actions workflow will automatically deploy the ingester to your home lab cluster using these secrets.

## Required GitHub Secrets

You need to set up the following secrets in your GitHub repository:

### 1. TELEGRAM_API_ID
- **Description**: Your Telegram API ID
- **How to get**: Go to [https://my.telegram.org/apps](https://my.telegram.org/apps)
- **Example**: `********`

### 2. TELEGRAM_API_HASH
- **Description**: Your Telegram API hash
- **How to get**: From the same page as API ID
- **Example**: `abcdef**********abcdef**********`

### 3. TELEGRAM_PHONE_NUMBER
- **Description**: Your phone number in international format
- **Format**: Must include country code with +
- **Example**: `+**********`

### 4. TELEGRAM_CHANNELS
- **Description**: JSON array of channel usernames to monitor
- **Format**: JSON array (without @ symbols)
- **Example**: `["example_channel", "another_channel", "news_channel"]`

### 5. TELEGRAM_BACKEND_API_TOKEN (Optional)
- **Description**: Bearer token for backend API authentication
- **Example**: `your-api-token-here`
- **Note**: Leave empty if not using backend authentication

## Setting Up GitHub Secrets

### Step 1: Get Telegram API Credentials

1. Go to [https://my.telegram.org/apps](https://my.telegram.org/apps)
2. Log in with your Telegram account
3. Click "Create application"
4. Fill in the application details:
   - **App title**: "Warda Telegram Ingester"
   - **Short name**: "warda-ingester"
   - **Platform**: "Desktop"
5. Note down your `api_id` and `api_hash`

### Step 2: Add Secrets to GitHub

1. **Go to your repository on GitHub**
2. **Click on "Settings"** (in the repository menu)
3. **Click on "Secrets and variables"** → **"Actions"**
4. **Click "New repository secret"** for each secret

#### Add each secret:

**TELEGRAM_API_ID:**
- Name: `TELEGRAM_API_ID`
- Value: `********` (your actual API ID)

**TELEGRAM_API_HASH:**
- Name: `TELEGRAM_API_HASH`
- Value: `abcdef**********abcdef**********` (your actual API hash)

**TELEGRAM_PHONE_NUMBER:**
- Name: `TELEGRAM_PHONE_NUMBER`
- Value: `+**********` (your actual phone number)

**TELEGRAM_CHANNELS:**
- Name: `TELEGRAM_CHANNELS`
- Value: `["example_channel", "another_channel"]` (your actual channels)

**TELEGRAM_BACKEND_API_TOKEN:** (Optional)
- Name: `TELEGRAM_BACKEND_API_TOKEN`
- Value: `your-token-here` (or leave empty)

## Deployment Process

Once the secrets are configured:

1. **Push to dev branch**:
   ```bash
   git push origin dev
   ```

2. **GitHub Actions will automatically**:
   - Build all images including telegram-ingester
   - Deploy to your home lab cluster (192.168.1.111)
   - Configure the Telegram ingester with your secrets
   - Start monitoring the specified channels

3. **First-time authentication**:
   - The first deployment will require Telegram authentication
   - Check the logs and follow the authentication process

## Monitoring Deployment

### Check GitHub Actions
1. Go to the "Actions" tab in your repository
2. Click on the latest "CI - Deploy Dev Cluster" workflow
3. Monitor the build and deployment progress

### Check Deployment Status
```bash
# SSH to your home lab server
ssh user@192.168.1.111

# Check if telegram-ingester is running
kubectl get pods -n warda -l app=telegram-ingester

# View logs
kubectl logs -n warda -f deployment/telegram-ingester

# Check configuration
kubectl describe configmap -n warda telegram-ingester-config
```

## First-Time Authentication

The first time the ingester runs, it needs to authenticate with Telegram:

1. **Check the logs**:
   ```bash
   kubectl logs -n warda -f deployment/telegram-ingester
   ```

2. **If you see authentication prompts**:
   ```bash
   # Connect to the pod
   kubectl exec -n warda -it deployment/telegram-ingester -- /bin/sh
   
   # Follow the prompts to enter verification code
   # The code will be sent to your Telegram account
   ```

3. **Session persistence**:
   - Once authenticated, the session is saved to persistent storage
   - Future deployments won't require re-authentication

## Updating Configuration

### To change channels:
1. Update the `TELEGRAM_CHANNELS` secret in GitHub
2. Push to the `dev` branch to redeploy

### To change credentials:
1. Update the relevant secrets in GitHub
2. Push to the `dev` branch to redeploy
3. May require re-authentication if credentials changed

## Security Best Practices

### ✅ Do:
- Use GitHub Secrets for all sensitive data
- Enable 2FA on your Telegram account
- Regularly review who has access to your repository
- Monitor deployment logs for unusual activity
- Use strong passwords for your GitHub account

### ❌ Don't:
- Never commit secrets to code
- Don't share API credentials
- Don't use personal channels for testing
- Don't disable authentication requirements

## Troubleshooting

### Common Issues:

**"Secret not found" errors:**
- Verify all required secrets are set in GitHub
- Check secret names match exactly (case-sensitive)

**"Authentication failed":**
- Verify API credentials are correct
- Check phone number format includes country code
- Ensure Telegram account has access to specified channels

**"Channel not found":**
- Verify channel usernames are correct (without @)
- Ensure you have access to the channels
- Some channels may need manual joining first

**"Deployment timeout":**
- Check if authentication is required (first-time setup)
- Monitor pod logs for specific error messages

### Reset Authentication:
```bash
# Delete the persistent volume to reset session
kubectl delete pvc -n warda telegram-ingester-data

# Restart the deployment
kubectl rollout restart -n warda deployment/telegram-ingester
```

## Workflow Configuration

The updated GitHub Actions workflow (`deploy_dev.yaml`) now:

- ✅ Builds telegram-ingester image
- ✅ Imports it to k3s cluster
- ✅ Deploys with Telegram ingester enabled
- ✅ Configures secrets from GitHub Secrets
- ✅ Sets up channel monitoring
- ✅ Handles authentication flow

## Example Secrets Configuration

Here's what your GitHub Secrets should look like:

```
TELEGRAM_API_ID: ********
TELEGRAM_API_HASH: abcdef**********abcdef**********
TELEGRAM_PHONE_NUMBER: +**********
TELEGRAM_CHANNELS: ["crypto_news", "tech_updates", "market_signals"]
TELEGRAM_BACKEND_API_TOKEN: (empty or your token)
```

Once configured, every push to `dev` will automatically deploy the Telegram ingester to your home lab!
