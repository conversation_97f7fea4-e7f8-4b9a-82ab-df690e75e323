# Telegram Ingester Setup Guide

This guide explains how to set up the Telegram ingester for your home lab deployment while keeping secrets secure.

## Overview

The Telegram ingester is **disabled by default** and only runs when explicitly enabled for your home lab cluster. It will never run on local k3d clusters or other environments unless specifically configured.

## Prerequisites

1. **Telegram API Credentials**: Get these from [https://my.telegram.org/apps](https://my.telegram.org/apps)
2. **Home Lab Cluster**: Running k3s with <PERSON><PERSON> installed
3. **Access to Telegram Channels**: You must have access to the channels you want to monitor

## Step 1: Get Telegram API Credentials

1. Go to [https://my.telegram.org/apps](https://my.telegram.org/apps)
2. Log in with your Telegram account
3. Click "Create application"
4. Fill in the required information:
   - **App title**: "Warda Telegram Ingester"
   - **Short name**: "warda-ingester"
   - **Platform**: Choose "Desktop"
5. Note down your `api_id` and `api_hash`

## Step 2: Create Secrets Configuration

1. **Copy the secrets template**:
   ```bash
   cp helm/warda/values-secrets.yaml.template helm/warda/values-secrets.yaml
   ```

2. **Edit the secrets file**:
   ```bash
   # Edit with your preferred editor
   nano helm/warda/values-secrets.yaml
   ```

3. **Fill in your credentials**:
   ```yaml
   telegramIngester:
     secrets:
       apiId: "********"  # Your API ID from my.telegram.org
       apiHash: "abcdef**********abcdef**********"  # Your API hash
       phoneNumber: "+**********"  # Your phone number in international format
       backendApiToken: ""  # Optional: leave empty for now
     
     config:
       channels:
         - "example_channel"  # Channel usernames without @
         - "another_channel"
   ```

## Step 3: Update Home Lab Configuration

1. **Edit the home lab values file**:
   ```bash
   nano helm/warda/values-homelab.yaml
   ```

2. **Update the cluster IP and registry**:
   ```yaml
   global:
     clusterHost: "*************"  # Your actual cluster IP
     image:
       registry: "your-registry.com"  # Your registry URL
   ```

## Step 4: Deploy to Home Lab

### Option A: Deploy with Telegram Ingester

```bash
# Build and deploy with Telegram ingester enabled
make build-multiarch
make helm-deploy-homelab
```

### Option B: Deploy without Telegram Ingester

```bash
# Deploy without Telegram ingester (if you want to test first)
make build-multiarch
make helm-deploy-homelab-no-telegram
```

## Step 5: First-Time Authentication

The first time the ingester runs, it needs to authenticate with Telegram:

1. **Check the ingester logs**:
   ```bash
   kubectl logs -f deployment/telegram-ingester
   ```

2. **If authentication is needed**, you'll see a message asking for a verification code

3. **Get the verification code** from your Telegram app (sent to your account)

4. **Enter the code** by connecting to the pod:
   ```bash
   kubectl exec -it deployment/telegram-ingester -- /bin/sh
   # Follow the prompts to enter the verification code
   ```

5. **The session will be saved** and future restarts won't require authentication

## Configuration Details

### Security Features

- ✅ **Secrets are never committed** to git (protected by .gitignore)
- ✅ **Environment variable override** for sensitive data
- ✅ **Kubernetes secrets** for credential storage
- ✅ **Persistent storage** for session data
- ✅ **Disabled by default** - only runs when explicitly enabled

### File Structure

```
helm/warda/
├── values.yaml                    # Main config (Telegram disabled)
├── values-homelab.yaml           # Home lab config (Telegram enabled)
├── values-secrets.yaml.template  # Template for secrets
├── values-secrets.yaml           # Your actual secrets (gitignored)
└── templates/
    ├── telegram-ingester-deployment.yaml
    ├── telegram-ingester-configmap.yaml
    ├── telegram-ingester-secret.yaml
    └── telegram-ingester-pvc.yaml
```

### Environment Variables

The ingester reads these environment variables (injected from Kubernetes secrets):

- `TELEGRAM_API_ID`: Your Telegram API ID
- `TELEGRAM_API_HASH`: Your Telegram API hash
- `TELEGRAM_PHONE_NUMBER`: Your phone number
- `BACKEND_API_TOKEN`: Optional backend authentication token

## Monitoring

### Check Ingester Status

```bash
# Check if the ingester is running
kubectl get pods -l app=telegram-ingester

# View logs
kubectl logs -f deployment/telegram-ingester

# Check configuration
kubectl describe configmap telegram-ingester-config
```

### Health Checks

The ingester sends periodic health checks to the backend API. You can monitor these in the backend logs:

```bash
kubectl logs -f deployment/warda-backend | grep "health check"
```

## Troubleshooting

### Common Issues

1. **"values-secrets.yaml not found"**
   - Create the file from the template as described in Step 2

2. **"Authentication failed"**
   - Verify your API credentials are correct
   - Ensure your phone number is in international format (+**********)

3. **"Channel not found"**
   - Ensure you have access to the channels
   - Check channel usernames are correct (without @)
   - Some channels may need to be joined manually first

4. **"Permission denied"**
   - Ensure your Telegram account has access to the channels
   - Some channels may require admin approval

### Reset Authentication

If you need to reset the Telegram authentication:

```bash
# Delete the persistent volume claim (this will delete the session)
kubectl delete pvc telegram-ingester-data

# Restart the deployment
kubectl rollout restart deployment/telegram-ingester
```

## Security Best Practices

1. **Never commit secrets** to git
2. **Use strong passwords** for your Telegram account
3. **Enable 2FA** on your Telegram account
4. **Regularly rotate** API credentials if possible
5. **Monitor access logs** for unusual activity
6. **Limit channel access** to only what's necessary

## Deployment Commands Reference

```bash
# Deploy to home lab with Telegram ingester
make helm-deploy-homelab

# Deploy to home lab without Telegram ingester
make helm-deploy-homelab-no-telegram

# Deploy to remote cluster (no Telegram ingester)
make helm-deploy-remote

# Build images for deployment
make build-multiarch
```
