#!/bin/bash

# Setup script for Telegram authentication in Kubernetes
# This script helps you authenticate the Telegram ingester for Kubernetes deployment

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
INGESTER_DIR="$PROJECT_ROOT/apps/telegram-ingester"
SESSION_FILE="$INGESTER_DIR/session.dat"

echo "🔐 Telegram Ingester Authentication Setup"
echo "========================================"
echo

# Check if session file already exists
if [ -f "$SESSION_FILE" ]; then
    echo "✅ Session file already exists at: $SESSION_FILE"
    echo "   You can skip authentication and proceed to deploy to Kubernetes."
    echo
    read -p "Do you want to re-authenticate? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Proceeding with existing session..."
        echo
    else
        echo "Removing existing session file..."
        rm -f "$SESSION_FILE"
    fi
fi

# Check if we need to authenticate
if [ ! -f "$SESSION_FILE" ]; then
    echo "📱 Setting up Telegram authentication..."
    echo
    echo "Before proceeding, make sure you have:"
    echo "1. Created a Telegram app at https://my.telegram.org/apps"
    echo "2. Updated the Config.toml file with your API credentials"
    echo
    read -p "Have you configured your API credentials in Config.toml? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Please configure your API credentials first:"
        echo "   Edit: $INGESTER_DIR/Config.toml"
        echo "   Add your api_id, api_hash, and phone_number"
        exit 1
    fi

    echo "🚀 Running authentication process..."
    echo "   You will be prompted to enter the verification code sent to your Telegram app."
    echo
    
    cd "$INGESTER_DIR"
    if cargo run; then
        echo
        echo "✅ Authentication successful!"
        echo "   Session saved to: $SESSION_FILE"
    else
        echo
        echo "❌ Authentication failed. Please check your configuration and try again."
        exit 1
    fi
fi

# Create Kubernetes secret
echo "📦 Creating Kubernetes secret..."
if kubectl get secret telegram-session >/dev/null 2>&1; then
    echo "   Secret already exists. Updating..."
    kubectl delete secret telegram-session
fi

if kubectl create secret generic telegram-session --from-file=session.dat="$SESSION_FILE"; then
    echo "✅ Kubernetes secret 'telegram-session' created successfully!"
else
    echo "❌ Failed to create Kubernetes secret. Make sure kubectl is configured and you have access to the cluster."
    exit 1
fi

echo
echo "🎉 Setup complete!"
echo "   You can now deploy the telegram-ingester to Kubernetes."
echo "   The session file will be automatically mounted in the pod."
echo
echo "Next steps:"
echo "1. Run: make k3d-deploy"
echo "2. Check logs: kubectl logs -l app.kubernetes.io/name=telegram-ingester"
