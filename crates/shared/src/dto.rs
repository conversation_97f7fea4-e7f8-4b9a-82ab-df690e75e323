use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;

// Legacy DTO - keeping for backward compatibility
#[derive(Debug, Serialize, Deserialize)]
pub struct MyDto {
    pub id: u32,
    pub name: String,
}

// Telegram-related DTOs

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TelegramUser {
    pub id: i64,
    pub username: Option<String>,
    pub first_name: Option<String>,
    pub last_name: Option<String>,
    pub phone_number: Option<String>,
    pub is_bot: bool,
    pub is_premium: bool,
    pub language_code: Option<String>,
    pub last_seen: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TelegramChannel {
    pub id: i64,
    pub title: String,
    pub username: Option<String>,
    pub description: Option<String>,
    pub member_count: Option<i32>,
    pub is_public: bool,
    pub is_verified: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TelegramMessage {
    pub id: Uuid,
    pub telegram_message_id: i64,
    pub channel_id: i64,
    pub sender_id: Option<i64>,
    pub content: String,
    pub message_type: MessageType,
    pub reply_to_message_id: Option<i64>,
    pub forward_from_channel_id: Option<i64>,
    pub forward_from_message_id: Option<i64>,
    pub edit_date: Option<DateTime<Utc>>,
    pub sent_at: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessageType {
    Text,
    Photo,
    Video,
    Audio,
    Document,
    Sticker,
    Voice,
    VideoNote,
    Contact,
    Location,
    Poll,
    Venue,
    Animation,
    Dice,
    Game,
    Invoice,
    SuccessfulPayment,
    ConnectedWebsite,
    PassportData,
    ProximityAlertTriggered,
    VoiceChatScheduled,
    VoiceChatStarted,
    VoiceChatEnded,
    VoiceChatParticipantsInvited,
    MessageAutoDeleteTimerChanged,
    MigrateToChatId,
    MigrateFromChatId,
    PinnedMessage,
    NewChatMembers,
    LeftChatMember,
    NewChatTitle,
    NewChatPhoto,
    DeleteChatPhoto,
    GroupChatCreated,
    SupergroupChatCreated,
    ChannelChatCreated,
    Unknown,
}

// API Request/Response DTOs for communication between ingester and backend

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateUserRequest {
    pub user: TelegramUser,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateUserResponse {
    pub success: bool,
    pub message: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateChannelRequest {
    pub channel: TelegramChannel,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateChannelResponse {
    pub success: bool,
    pub message: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateMessageRequest {
    pub message: TelegramMessage,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateMessageResponse {
    pub success: bool,
    pub message: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BatchCreateMessagesRequest {
    pub messages: Vec<TelegramMessage>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BatchCreateMessagesResponse {
    pub success: bool,
    pub processed_count: usize,
    pub failed_count: usize,
    pub message: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GetChannelStatsRequest {
    pub channel_id: i64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GetChannelStatsResponse {
    pub channel_id: i64,
    pub message_count: i64,
    pub user_count: i64,
    pub last_message_at: Option<DateTime<Utc>>,
}

// Health check and status DTOs

#[derive(Debug, Serialize, Deserialize)]
pub struct IngesterStatus {
    pub status: String,
    pub connected_channels: Vec<i64>,
    pub messages_processed: u64,
    pub last_activity: DateTime<Utc>,
    pub uptime_seconds: u64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct IngesterHealthResponse {
    pub healthy: bool,
    pub status: IngesterStatus,
}