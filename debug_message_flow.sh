#!/bin/bash

# Debug script to monitor message flow through the Warda system
# Usage: ./debug_message_flow.sh [backend_url]

BACKEND_URL=${1:-"http://localhost:3000"}
LOG_FILE="debug_message_flow.log"

echo "=== Warda Message Flow Debug Tool ===" | tee -a $LOG_FILE
echo "Backend URL: $BACKEND_URL" | tee -a $LOG_FILE
echo "Timestamp: $(date)" | tee -a $LOG_FILE
echo "" | tee -a $LOG_FILE

# Function to make API calls and log responses
check_endpoint() {
    local endpoint=$1
    local method=${2:-GET}
    local description=$3
    
    echo "--- $description ---" | tee -a $LOG_FILE
    echo "Endpoint: $method $endpoint" | tee -a $LOG_FILE
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" "$BACKEND_URL$endpoint" 2>&1)
    else
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X "$method" "$BACKEND_URL$endpoint" 2>&1)
    fi
    
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    body=$(echo "$response" | sed '/HTTP_CODE:/d')
    
    echo "HTTP Code: $http_code" | tee -a $LOG_FILE
    echo "Response: $body" | tee -a $LOG_FILE
    echo "" | tee -a $LOG_FILE
}

# Function to check logs
check_logs() {
    echo "--- Recent Backend Logs ---" | tee -a $LOG_FILE
    if command -v kubectl &> /dev/null; then
        echo "Checking Kubernetes logs..." | tee -a $LOG_FILE
        kubectl logs -l app=backend --tail=20 2>/dev/null | tee -a $LOG_FILE || echo "No Kubernetes backend pods found" | tee -a $LOG_FILE
    else
        echo "kubectl not available, skipping Kubernetes logs" | tee -a $LOG_FILE
    fi
    echo "" | tee -a $LOG_FILE
}

# Function to test message creation
test_message_creation() {
    echo "--- Testing Message Creation ---" | tee -a $LOG_FILE
    
    # Test single message creation
    test_payload='{
        "message": {
            "telegram_message_id": 999999,
            "channel_id": -1001234567890,
            "sender_id": 123456789,
            "content": "Test message from debug script",
            "message_type": "text",
            "sent_at": "'$(date -u +%Y-%m-%dT%H:%M:%S%.3fZ)'",
            "reply_to_message_id": null,
            "forward_from_chat_id": null,
            "forward_from_message_id": null,
            "media_type": null,
            "media_file_id": null,
            "media_file_size": null
        }
    }'
    
    echo "Sending test message..." | tee -a $LOG_FILE
    response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -d "$test_payload" \
        "$BACKEND_URL/api/telegram/messages" 2>&1)
    
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    body=$(echo "$response" | sed '/HTTP_CODE:/d')
    
    echo "HTTP Code: $http_code" | tee -a $LOG_FILE
    echo "Response: $body" | tee -a $LOG_FILE
    echo "" | tee -a $LOG_FILE
}

# Main execution
echo "Starting debug checks..." | tee -a $LOG_FILE

# Check basic endpoints
check_endpoint "/health" "GET" "Health Check"
check_endpoint "/debug" "GET" "Debug Info"
check_endpoint "/api/telegram/debug/database" "GET" "Database Debug"

# Test message creation
test_message_creation

# Check logs
check_logs

echo "=== Debug Complete ===" | tee -a $LOG_FILE
echo "Full log saved to: $LOG_FILE"
echo ""
echo "To monitor real-time logs:"
echo "  Backend: kubectl logs -f -l app=backend"
echo "  Telegram Ingester: kubectl logs -f -l app=telegram-ingester"
echo ""
echo "To check database directly:"
echo "  kubectl exec -it deployment/postgresql -- psql -U postgres -d warda -c 'SELECT COUNT(*) FROM telegram_messages;'"
