use axum::{
    error_handling::HandleError<PERSON>ayer,
    extract::State,
    http::{StatusCode, HeaderValue, Method},
    response::IntoResponse,
    routing::{get, post},
    Json, Router,
};

mod telegram_handlers;
use serde::{Deserialize, Serialize};
use serde_json::json;
use tower_http::cors::{CorsLayer, Any};

use std::{
    net::SocketAddr,
    time::Duration,
};
use tower::{BoxError, ServiceBuilder};
use tower_http::trace::TraceLayer;
use tracing_subscriber::EnvFilter;

#[derive(Debug, Deserialize, Serialize, Clone)]
struct Config {
    server: ServerConfig,
    database: DatabaseConfig,
    log: LogConfig,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
struct ServerConfig {
    host: String,
    port: u16,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
struct DatabaseConfig {
    url: String,
}

#[derive(Debug, Deserialize, Serialize, <PERSON>lone)]
struct LogConfig {
    level: String,
}

fn load_config() -> Result<Config, Box<dyn std::error::Error>> {
    let config_path = std::env::var("CONFIG_PATH").unwrap_or_else(|_| "Config.toml".to_string());
    println!("Loading config from: {}", config_path);

    let config_content = std::fs::read_to_string(&config_path)
        .map_err(|e| format!("Failed to read config file '{}': {}", config_path, e))?;

    let mut config: Config = toml::from_str(&config_content)
        .map_err(|e| format!("Failed to parse config file '{}': {}", config_path, e))?;

    // Override database URL with environment variable if present
    println!("Checking for DATABASE_URL environment variable...");
    match std::env::var("DATABASE_URL") {
        Ok(database_url) => {
            println!("Overriding database URL from environment variable: {}", database_url);
            config.database.url = database_url;
        }
        Err(e) => {
            println!("No DATABASE_URL environment variable found: {}", e);
        }
    }

    println!("Final config: {:?}", config);
    Ok(config)
}

#[tokio::main]
async fn main() {
    eprintln!("Backend starting...");

    // Load configuration
    let config = match load_config() {
        Ok(config) => config,
        Err(e) => {
            eprintln!("Failed to load configuration: {}", e);
            std::process::exit(1);
        }
    };

    println!("Setting up tracing...");
    tracing_subscriber::fmt()
        .with_env_filter(
            EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| format!("backend={},tower_http={}", config.log.level, config.log.level).into()),
        )
        .init();

    println!("Tracing initialized");

    let state = AppState::default();
    println!("Created app state");

    let app = Router::new()
        .route("/debug", get(debug_handler))
        .route("/health", get(health_handler))
        .route("/auth/validate", post(validate_token_handler))
        // Telegram API routes
        // .route("/api/telegram/users", post(telegram_handlers::create_user))
        // .route("/api/telegram/channels", post(telegram_handlers::create_channel))
        // .route("/api/telegram/messages", post(telegram_handlers::create_message))
        // .route("/api/telegram/messages/batch", post(telegram_handlers::create_messages_batch))
        // .route("/api/telegram/channels/:channel_id/stats", get(telegram_handlers::get_channel_stats))
        // .route("/api/telegram/ingester/health", post(telegram_handlers::ingester_health))
        // .route("/api/telegram/debug/database", get(telegram_handlers::debug_database_status))
        .layer(
            ServiceBuilder::new()
                .layer(HandleErrorLayer::new(|error: BoxError| async move {
                    if error.is::<tower::timeout::error::Elapsed>() {
                        Ok(StatusCode::REQUEST_TIMEOUT)
                    } else {
                        Err((
                            StatusCode::INTERNAL_SERVER_ERROR,
                            format!("Unhandled internal error: {error}"),
                        ))
                    }
                }))
                .timeout(Duration::from_secs(10))
                .layer(TraceLayer::new_for_http())
                .layer(
                    CorsLayer::new()
                        .allow_origin(Any)
                        .allow_methods([Method::GET, Method::POST, Method::OPTIONS])
                        .allow_headers(Any)
                )
                .into_inner(),
        )
        .with_state(state);

    println!("Created router");
    println!("Telegram routes registered successfully");

    let addr = format!("{}:{}", config.server.host, config.server.port);
    println!("Attempting to bind to address: {}", addr);
    tracing::debug!("listening on {}", addr);
    
    match tokio::net::TcpListener::bind(&addr).await {
        Ok(listener) => {
            println!("Successfully bound to {}", addr);
            println!("Starting server...");
            if let Err(e) = axum::serve(listener, app).await {
                eprintln!("Server error: {}", e);
                std::process::exit(1);
            }
        }
        Err(e) => {
            eprintln!("Failed to bind to {}: {}", addr, e);
            std::process::exit(1);
        }
    }
    
    tracing::info!("backend stopped");
    println!("Backend stopped");
}

#[derive(Clone, Default)]
struct AppState {
    // Add shared app state here if needed later
}

async fn health_handler(State(_state): State<AppState>) -> impl IntoResponse {
    Json(json!({
        "status": "ok",
        "timestamp": chrono::Utc::now().to_rfc3339(),
    }))
}

async fn debug_handler(State(_state): State<AppState>) -> impl IntoResponse {
    Json(json!({
        "status": "ok",
        "version": "0.1.0",
        "uptime": format!("{}", chrono::Utc::now()),
    }))
}

async fn validate_token_handler(State(_state): State<AppState>) -> impl IntoResponse {
    // For now, just return OK - in a real implementation, you would validate the JWT token
    // against Keycloak or your authentication provider
    tracing::info!("Token validation request received");
    Json(json!({
        "valid": true,
        "message": "Token validation successful"
    }))
}