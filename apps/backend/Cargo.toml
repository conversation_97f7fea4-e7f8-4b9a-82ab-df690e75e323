[package]
name = "backend"
version = "0.1.0"
edition = "2021"

[dependencies]
shared = { path = "../../crates/shared" }

axum = "0.8.4"
serde = { version = "1.0", features = ["derive"] }
serde_json = { version = "1.0.141" }
toml = "0.8"
tokio = { version = "1.0", features = ["full"] }
tower = { version = "0.5.2", features = ["util", "timeout"] }
tower-http = { version = "0.6.1", features = ["add-extension", "trace", "cors"] }
tracing = "0.1"
chrono = "0.4.41"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }

# Database
sqlx = { version = "0.8", features = ["runtime-tokio-rustls", "postgres", "chrono", "uuid"] }
uuid = { version = "1.0", features = ["v4"] }

[[bin]]
name = "backend"
path = "src/main.rs"
