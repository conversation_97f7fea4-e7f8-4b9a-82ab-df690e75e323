-- Create Telegram Users table
CREATE TABLE telegram_users (
    id BIGINT PRIMARY KEY,
    username <PERSON><PERSON><PERSON><PERSON>(255),
    first_name <PERSON><PERSON><PERSON><PERSON>(255),
    last_name <PERSON><PERSON><PERSON><PERSON>(255),
    phone_number <PERSON><PERSON><PERSON><PERSON>(50),
    is_bot BOOLEAN NOT NULL DEFAULT FALSE,
    is_premium BOOLEAN NOT NULL DEFAULT FALSE,
    language_code <PERSON><PERSON><PERSON><PERSON>(10),
    last_seen TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create index on username for faster lookups
CREATE INDEX idx_telegram_users_username ON telegram_users(username) WHERE username IS NOT NULL;

-- Create Telegram Channels table
CREATE TABLE telegram_channels (
    id BIGINT PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    username VA<PERSON>HA<PERSON>(255),
    description TEXT,
    member_count INTEGER,
    is_public BOOLEAN NOT NULL DEFAULT FALSE,
    is_verified BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create index on username for faster lookups
CREATE INDEX idx_telegram_channels_username ON telegram_channels(username) WHERE username IS NOT NULL;

-- Create Telegram Messages table
CREATE TABLE telegram_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    telegram_message_id BIGINT NOT NULL,
    channel_id BIGINT NOT NULL REFERENCES telegram_channels(id),
    sender_id BIGINT REFERENCES telegram_users(id),
    content TEXT NOT NULL,
    message_type VARCHAR(50) NOT NULL,
    reply_to_message_id BIGINT,
    forward_from_channel_id BIGINT REFERENCES telegram_channels(id),
    forward_from_message_id BIGINT,
    edit_date TIMESTAMPTZ,
    sent_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX idx_telegram_messages_channel_id ON telegram_messages(channel_id);
CREATE INDEX idx_telegram_messages_sender_id ON telegram_messages(sender_id) WHERE sender_id IS NOT NULL;
CREATE INDEX idx_telegram_messages_sent_at ON telegram_messages(sent_at);
CREATE INDEX idx_telegram_messages_telegram_id ON telegram_messages(telegram_message_id);

-- Create unique constraint to prevent duplicate messages from the same channel
CREATE UNIQUE INDEX idx_telegram_messages_unique ON telegram_messages(channel_id, telegram_message_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at
CREATE TRIGGER update_telegram_users_updated_at 
    BEFORE UPDATE ON telegram_users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_telegram_channels_updated_at 
    BEFORE UPDATE ON telegram_channels 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create view for message statistics by channel
CREATE VIEW telegram_channel_stats AS
SELECT 
    c.id as channel_id,
    c.title as channel_title,
    c.username as channel_username,
    COUNT(m.id) as message_count,
    COUNT(DISTINCT m.sender_id) as unique_senders,
    MAX(m.sent_at) as last_message_at,
    MIN(m.sent_at) as first_message_at
FROM telegram_channels c
LEFT JOIN telegram_messages m ON c.id = m.channel_id
GROUP BY c.id, c.title, c.username;

-- Create view for user activity statistics
CREATE VIEW telegram_user_stats AS
SELECT 
    u.id as user_id,
    u.username,
    u.first_name,
    u.last_name,
    COUNT(m.id) as message_count,
    COUNT(DISTINCT m.channel_id) as channels_active_in,
    MAX(m.sent_at) as last_message_at,
    MIN(m.sent_at) as first_message_at
FROM telegram_users u
LEFT JOIN telegram_messages m ON u.id = m.sender_id
GROUP BY u.id, u.username, u.first_name, u.last_name;
