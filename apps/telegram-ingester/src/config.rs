use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use std::fs;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Config {
    pub telegram: TelegramConfig,
    pub backend_api_url: String,
    pub backend_api_token: Option<String>,
    pub log: LogConfig,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TelegramConfig {
    pub api_id: i32,
    pub api_hash: String,
    pub phone_number: String,
    pub session_file: String,
    pub channels: Vec<String>,
    pub max_messages_per_batch: usize,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct LogConfig {
    pub level: String,
}

impl Config {
    pub fn load() -> Result<Self> {
        let config_path = std::env::var("CONFIG_PATH")
            .unwrap_or_else(|_| "Config.toml".to_string());

        let config_content = fs::read_to_string(&config_path)
            .with_context(|| format!("Failed to read config file: {}", config_path))?;

        let mut config: Config = toml::from_str(&config_content)
            .with_context(|| format!("Failed to parse config file: {}", config_path))?;

        // Override with environment variables if present
        if let Ok(api_id) = std::env::var("TELEGRAM_API_ID") {
            config.telegram.api_id = api_id.parse()
                .with_context(|| "Failed to parse TELEGRAM_API_ID as integer")?;
        }

        if let Ok(api_hash) = std::env::var("TELEGRAM_API_HASH") {
            config.telegram.api_hash = api_hash;
        }

        if let Ok(phone_number) = std::env::var("TELEGRAM_PHONE_NUMBER") {
            config.telegram.phone_number = phone_number;
        }

        if let Ok(backend_token) = std::env::var("BACKEND_API_TOKEN") {
            config.backend_api_token = Some(backend_token);
        }

        Ok(config)
    }
}

impl Default for Config {
    fn default() -> Self {
        Self {
            telegram: TelegramConfig {
                api_id: 0,
                api_hash: String::new(),
                phone_number: String::new(),
                session_file: "session.dat".to_string(),
                channels: vec![],
                max_messages_per_batch: 100,
            },
            backend_api_url: "http://localhost:3000".to_string(),
            backend_api_token: None,
            log: LogConfig {
                level: "info".to_string(),
            },
        }
    }
}
