use anyhow::{Context, Result};
use grammers_client::{Client, Config as ClientConfig, Update};
use grammers_session::Session;
use shared::{TelegramMessage, TelegramUser, TelegramChannel, MessageType};
use std::collections::HashMap;
use std::io::{self, Write};
use tracing::{info, warn};
use chrono::Utc;
use uuid::Uuid;

use crate::config::TelegramConfig;

pub struct TelegramClient {
    client: Client,
    config: TelegramConfig,
    joined_channels: HashMap<String, i64>,
    message_buffer: Vec<TelegramMessage>,
}

impl TelegramClient {
    pub async fn new(config: &TelegramConfig) -> Result<Self> {
        info!("Initializing Telegram client...");
        
        // Load or create session
        let session = Session::load_file_or_create(&config.session_file)?;

        // Create the client
        let client = Client::connect(ClientConfig {
            session,
            api_id: config.api_id,
            api_hash: config.api_hash.clone(),
            params: Default::default(),
        })
        .await
        .context("Failed to connect to Telegram")?;

        info!("Telegram client created successfully");

        Ok(Self {
            client,
            config: config.clone(),
            joined_channels: HashMap::new(),
            message_buffer: Vec::new(),
        })
    }

    pub async fn connect(&mut self) -> Result<()> {
        info!("Connecting to Telegram...");
        
        // Check if we're already signed in
        if !self.client.is_authorized().await? {
            info!("Not authorized, requesting login code...");

            // Request login code
            let token = self.client.request_login_code(&self.config.phone_number).await?;
            info!("Login code requested successfully. Check your Telegram app for the verification code.");

            // Get verification code from environment variable or user input
            let code = if let Ok(env_code) = std::env::var("TELEGRAM_VERIFICATION_CODE") {
                info!("Using verification code from environment variable");
                env_code.trim().to_string()
            } else {
                print!("Enter the verification code sent to your Telegram account: ");
                io::stdout().flush()?;

                let mut code = String::new();
                io::stdin().read_line(&mut code)?;
                code.trim().to_string()
            };

            info!("Attempting to sign in with verification code...");

            // Sign in with the verification code
            match self.client.sign_in(&token, &code).await {
                Ok(_) => {
                    info!("Successfully authenticated with Telegram!");

                    // Save the session after successful authentication
                    info!("Saving session to: {}", &self.config.session_file);
                    if let Err(e) = self.save_session().await {
                        warn!("Failed to save session: {}. You may need to authenticate again next time.", e);
                    } else {
                        info!("Session saved successfully. Future runs won't require authentication.");
                    }
                }
                Err(e) => {
                    return Err(anyhow::anyhow!(
                        "Failed to authenticate with Telegram: {}. Please check your verification code and try again.", e
                    ));
                }
            }
        } else {
            info!("Already authenticated with Telegram (using saved session)");
        }

        info!("Successfully connected to Telegram");
        Ok(())
    }

    pub async fn join_channel(&mut self, channel_identifier: &str) -> Result<()> {
        info!("Attempting to join channel: {}", channel_identifier);

        // Check if it's a chat ID (starts with - or is all digits) or a username
        let chat = if channel_identifier.starts_with('-') || channel_identifier.chars().all(|c| c.is_ascii_digit()) {
            // It's a chat ID - parse it and get the chat directly
            let chat_id: i64 = channel_identifier.parse()
                .with_context(|| format!("Failed to parse chat ID: {}", channel_identifier))?;

            info!("Using chat ID: {}", chat_id);

            // For chat IDs, we'll add it to our joined channels and trust that we have access
            // The grammers 0.7.0 API doesn't have get_chat method, so we'll verify access when messages arrive
            self.joined_channels.insert(channel_identifier.to_string(), chat_id);
            info!("Successfully added chat ID: {} (ID: {})", channel_identifier, chat_id);
            return Ok(());
        } else {
            // It's a username - resolve it
            info!("Resolving username: {}", channel_identifier);
            self.client.resolve_username(channel_identifier).await?
        };

        if let Some(chat) = chat {
            let channel_id = chat.id();
            self.joined_channels.insert(channel_identifier.to_string(), channel_id);
            info!("Successfully joined channel: {} (ID: {})", channel_identifier, channel_id);
            Ok(())
        } else {
            Err(anyhow::anyhow!("Channel not found: {}", channel_identifier))
        }
    }

    pub async fn get_new_messages(&mut self) -> Result<Vec<TelegramMessage>> {
        let mut messages = Vec::new();

        // Try to get updates from Telegram with a timeout
        // Use a non-blocking approach to avoid hanging
        match tokio::time::timeout(std::time::Duration::from_millis(100), self.client.next_update()).await {
            Ok(Ok(update)) => {
                info!("Received update: {:?}", update);

                match update {
                    Update::NewMessage(message) => {
                        info!("Processing new message from chat: {}", message.chat().id());
                        if let Some(telegram_message) = self.convert_message(message).await {
                            info!("Received message from chat {}: {}",
                                  telegram_message.channel_id,
                                  telegram_message.content.chars().take(50).collect::<String>());
                            messages.push(telegram_message);
                        }
                    }
                    _ => {
                        // Other update types we don't care about for now
                        // The trace logs show that channel messages come through as NewMessage updates
                        info!("Received other update type (ignoring)");
                    }
                }
            }
            Ok(Err(e)) => {
                warn!("Error getting updates: {}", e);
            }
            Err(_) => {
                // Timeout - no new messages, this is normal
            }
        }

        Ok(messages)
    }

    async fn convert_message(&self, message: grammers_client::types::Message) -> Option<TelegramMessage> {
        // Convert grammers message to our TelegramMessage DTO
        let chat = message.chat();
        let channel_id = chat.id();

        // Only process messages from channels we're monitoring
        if !self.joined_channels.values().any(|&id| id == channel_id) {
            return None;
        }

        let sender_id = message.sender().map(|s| s.id());
        let content = message.text().to_string();
        
        // Determine message type based on message content
        // In grammers 0.7.0, we need to check the media field
        let message_type = if message.photo().is_some() {
            MessageType::Photo
        } else if !content.is_empty() {
            MessageType::Text
        } else {
            MessageType::Unknown
        };

        let now = Utc::now();
        
        Some(TelegramMessage {
            id: Uuid::new_v4(),
            telegram_message_id: message.id() as i64,
            channel_id,
            sender_id,
            content,
            message_type,
            reply_to_message_id: message.reply_to_message_id().map(|id| id as i64),
            forward_from_channel_id: None, // TODO: Extract from forward info
            forward_from_message_id: None, // TODO: Extract from forward info
            edit_date: message.edit_date().map(|d| d),
            sent_at: message.date(),
            created_at: now,
        })
    }

    pub async fn get_channel_info(&self, channel_username: &str) -> Result<Option<TelegramChannel>> {
        if let Some(chat) = self.client.resolve_username(channel_username).await? {
            let now = Utc::now();

            let title = match &chat {
                grammers_client::types::Chat::User(user) => {
                    format!("{} {}", user.first_name(), user.last_name().unwrap_or(""))
                }
                grammers_client::types::Chat::Group(group) => group.title().to_string(),
                grammers_client::types::Chat::Channel(channel) => channel.title().to_string(),
            };

            let channel = TelegramChannel {
                id: chat.id(),
                title,
                username: Some(channel_username.to_string()),
                description: None, // TODO: Get description if available
                member_count: None, // TODO: Get member count if available
                is_public: true, // Assuming public if we can resolve by username
                is_verified: false, // TODO: Check verification status
                created_at: now,
                updated_at: now,
            };

            Ok(Some(channel))
        } else {
            Ok(None)
        }
    }

    pub async fn get_user_info(&self, _user_id: i64) -> Result<Option<TelegramUser>> {
        // TODO: Implement user info retrieval
        // This would require additional API calls to get user details
        Ok(None)
    }

    async fn save_session(&self) -> Result<()> {
        // Get the session from the client and save it to file
        let session = self.client.session();
        session.save_to_file(&self.config.session_file)
            .context("Failed to save session to file")?;
        Ok(())
    }
}
