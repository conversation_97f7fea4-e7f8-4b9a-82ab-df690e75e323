use anyhow::{Context, Result};
use reqwest::{Client, Response};
use serde_json;
use shared::{
    CreateUserRequest, CreateUserResponse,
    CreateChannelRequest, CreateChannelResponse,
    CreateMessageRequest, CreateMessageResponse,
    BatchCreateMessagesRequest, BatchCreateMessagesResponse,
    GetChannelStatsResponse,
    IngesterHealthResponse, IngesterStatus
};
use std::time::Duration;
use tracing::{debug, info, warn};
use chrono::Utc;

#[derive(Clone)]
pub struct ApiClient {
    client: Client,
    base_url: String,
    auth_token: Option<String>,
}

impl ApiClient {
    pub fn new(base_url: &str, auth_token: &Option<String>) -> Result<Self> {
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .build()
            .context("Failed to create HTTP client")?;

        Ok(Self {
            client,
            base_url: base_url.trim_end_matches('/').to_string(),
            auth_token: auth_token.clone(),
        })
    }

    pub async fn create_user(&self, request: CreateUserRequest) -> Result<CreateUserResponse> {
        let url = format!("{}/api/telegram/users", self.base_url);
        
        debug!("Creating user: {}", request.user.id);
        
        let response = self.post(&url, &request).await?;
        let result: CreateUserResponse = self.parse_response(response).await?;
        
        Ok(result)
    }

    pub async fn create_channel(&self, request: CreateChannelRequest) -> Result<CreateChannelResponse> {
        let url = format!("{}/api/telegram/channels", self.base_url);
        
        debug!("Creating channel: {}", request.channel.id);
        
        let response = self.post(&url, &request).await?;
        let result: CreateChannelResponse = self.parse_response(response).await?;
        
        Ok(result)
    }

    pub async fn create_message(&self, request: CreateMessageRequest) -> Result<CreateMessageResponse> {
        let url = format!("{}/api/telegram/messages", self.base_url);
        
        debug!("Creating message: {}", request.message.telegram_message_id);
        
        let response = self.post(&url, &request).await?;
        let result: CreateMessageResponse = self.parse_response(response).await?;
        
        Ok(result)
    }

    pub async fn create_messages_batch(&self, request: BatchCreateMessagesRequest) -> Result<BatchCreateMessagesResponse> {
        let url = format!("{}/api/telegram/messages/batch", self.base_url);
        
        debug!("Creating message batch: {} messages", request.messages.len());
        
        let response = self.post(&url, &request).await?;
        let result: BatchCreateMessagesResponse = self.parse_response(response).await?;
        
        Ok(result)
    }

    pub async fn get_channel_stats(&self, channel_id: i64) -> Result<GetChannelStatsResponse> {
        let url = format!("{}/api/telegram/channels/{}/stats", self.base_url, channel_id);
        
        debug!("Getting channel stats for: {}", channel_id);
        
        let response = self.get(&url).await?;
        let result: GetChannelStatsResponse = self.parse_response(response).await?;
        
        Ok(result)
    }

    pub async fn send_health_check(&self) -> Result<()> {
        let url = format!("{}/api/telegram/ingester/health", self.base_url);
        
        let status = IngesterStatus {
            status: "running".to_string(),
            connected_channels: vec![], // TODO: Get from telegram client
            messages_processed: 0, // TODO: Track this
            last_activity: Utc::now(),
            uptime_seconds: 0, // TODO: Track this
        };

        let health_response = IngesterHealthResponse {
            healthy: true,
            status,
        };
        
        debug!("Sending health check");
        
        let response = self.post(&url, &health_response).await?;
        
        if response.status().is_success() {
            debug!("Health check sent successfully");
        } else {
            warn!("Health check failed with status: {}", response.status());
        }
        
        Ok(())
    }

    async fn post<T: serde::Serialize>(&self, url: &str, body: &T) -> Result<Response> {
        let mut request = self.client.post(url);
        
        // Add authentication header if token is provided
        if let Some(token) = &self.auth_token {
            request = request.header("Authorization", format!("Bearer {}", token));
        }
        
        let response = request
            .json(body)
            .send()
            .await
            .with_context(|| format!("Failed to send POST request to {}", url))?;
        
        Ok(response)
    }

    async fn get(&self, url: &str) -> Result<Response> {
        let mut request = self.client.get(url);
        
        // Add authentication header if token is provided
        if let Some(token) = &self.auth_token {
            request = request.header("Authorization", format!("Bearer {}", token));
        }
        
        let response = request
            .send()
            .await
            .with_context(|| format!("Failed to send GET request to {}", url))?;
        
        Ok(response)
    }

    async fn parse_response<T: serde::de::DeserializeOwned>(&self, response: Response) -> Result<T> {
        let status = response.status();
        let url = response.url().clone();
        
        if !status.is_success() {
            let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            return Err(anyhow::anyhow!(
                "API request failed with status {}: {} (URL: {})", 
                status, error_text, url
            ));
        }
        
        let text = response.text().await
            .with_context(|| format!("Failed to read response body from {}", url))?;
        
        serde_json::from_str(&text)
            .with_context(|| format!("Failed to parse JSON response from {}: {}", url, text))
    }

    pub async fn test_connection(&self) -> Result<()> {
        let url = format!("{}/health", self.base_url);
        
        info!("Testing connection to backend API: {}", url);
        
        let response = self.get(&url).await?;
        
        if response.status().is_success() {
            info!("Successfully connected to backend API");
            Ok(())
        } else {
            Err(anyhow::anyhow!(
                "Backend API health check failed with status: {}", 
                response.status()
            ))
        }
    }
}
