use anyhow::{Context, Result};
use chrono::Utc;

use std::time::Duration;
use tokio::time::sleep;
use tracing::{error, info, warn};
use tracing_subscriber::EnvFilter;

mod config;
mod telegram_client;
mod api_client;
mod message_processor;

use config::Config;
use telegram_client::TelegramClient;
use api_client::ApiClient;
use message_processor::MessageProcessor;

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    tracing_subscriber::fmt()
        .with_env_filter(
            EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "telegram_ingester=info".into()),
        )
        .init();

    info!("Starting Telegram Ingester...");

    // Load configuration
    let config = Config::load().context("Failed to load configuration")?;
    info!("Configuration loaded successfully");

    // Initialize API client
    let api_client = ApiClient::new(&config.backend_api_url, &config.backend_api_token)
        .context("Failed to initialize API client")?;

    // Initialize message processor
    let mut message_processor = MessageProcessor::new(api_client.clone());

    // Initialize Telegram client
    let mut telegram_client = TelegramClient::new(&config.telegram)
        .await
        .context("Failed to initialize Telegram client")?;

    info!("Telegram client initialized successfully");

    // Connect to Telegram
    telegram_client.connect().await
        .context("Failed to connect to Telegram")?;

    info!("Connected to Telegram successfully");


    // Join configured channels
    for channel in &config.telegram.channels {
        match telegram_client.join_channel(channel).await {
            Ok(_) => info!("Successfully joined channel: {}", channel),
            Err(e) => warn!("Failed to join channel {}: {}", channel, e),
        }
    }

    // Start message processing loop
    info!("Starting message processing loop...");
    
    let mut last_health_check = Utc::now();
    let health_check_interval = Duration::from_secs(60); // 1 minute

    loop {
        // Process new messages
        match telegram_client.get_new_messages().await {
            Ok(messages) => {
                if !messages.is_empty() {
                    info!("Received {} new messages", messages.len());
                    
                    for message in messages {
                        if let Err(e) = message_processor.process_message(message).await {
                            error!("Failed to process message: {}", e);
                        }
                    }
                }
            }
            Err(e) => {
                error!("Failed to get new messages: {}", e);
                sleep(Duration::from_secs(5)).await;
                continue;
            }
        }

        // Periodic health check
        let now = Utc::now();
        if now.signed_duration_since(last_health_check).num_seconds() > health_check_interval.as_secs() as i64 {
            if let Err(e) = api_client.send_health_check().await {
                warn!("Failed to send health check: {}", e);
            }
            last_health_check = now;
        }

        // Small delay to prevent busy waiting
        sleep(Duration::from_millis(100)).await;
    }
}
