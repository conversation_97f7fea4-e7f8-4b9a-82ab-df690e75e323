[package]
name = "telegram-ingester"
version = "0.1.0"
edition = "2021"

[dependencies]
# Shared DTOs
shared = { path = "../../crates/shared" }

# Async runtime
tokio = { version = "1.0", features = ["full"] }

# HTTP client for API communication
reqwest = { version = "0.12", features = ["json"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Configuration
toml = "0.8"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }

# Time handling
chrono = { version = "0.4", features = ["serde"] }

# UUID generation
uuid = { version = "1.0", features = ["v4"] }

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Telegram client - we'll use grammers (pure Rust Telegram client)
grammers-client = "0.7.0"
grammers-session = "0.7.0"

[[bin]]
name = "telegram-ingester"
path = "src/main.rs"
