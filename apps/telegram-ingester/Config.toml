# Telegram Ingester Configuration

[telegram]
# Get these from https://my.telegram.org/apps
api_id = 0  # Replace with your API ID
api_hash = ""  # Replace with your API hash
phone_number = ""  # Replace with your phone number (e.g., "+1234567890")

# Session file to store authentication data
session_file = "session.dat"

# List of channels to monitor (usernames without @)
channels = [
    # "example_channel",
    # "another_channel"
]

# Maximum number of messages to send in a single batch
max_messages_per_batch = 100

# Backend API configuration
backend_api_url = "http://localhost:3000"
backend_api_token = ""  # Optional: Bearer token for API authentication

[log]
level = "debug"  # Options: trace, debug, info, warn, error
