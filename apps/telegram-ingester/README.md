# Telegram Ingester

A Rust-based Telegram client that monitors channels, captures messages, and sends data to the Warda backend API for persistence in PostgreSQL.

## Features

- **Channel Monitoring**: Join and monitor multiple Telegram channels
- **Message Processing**: Capture all message types (text, media, etc.)
- **User Tracking**: Track user information and activity
- **Batch Processing**: Efficient batch sending of messages to backend
- **Error Handling**: Robust retry logic and error recovery
- **Health Monitoring**: Regular health checks to backend API

## Setup

### 1. Get Telegram API Credentials

1. Go to [https://my.telegram.org/apps](https://my.telegram.org/apps)
2. Log in with your Telegram account
3. Create a new application
4. Note down your `api_id` and `api_hash`

### 2. Configure the Ingester

Copy the configuration template and fill in your details:

```bash
cp apps/telegram-ingester/Config.toml apps/telegram-ingester/Config.local.toml
```

Edit `Config.local.toml`:

```toml
[telegram]
api_id = ********  # Your API ID from my.telegram.org
api_hash = "your_api_hash_here"  # Your API hash
phone_number = "+**********"  # Your phone number
session_file = "session.dat"
channels = [
    "example_channel",  # Channel usernames without @
    "another_channel"
]
max_messages_per_batch = 100

backend_api_url = "http://localhost:3000"
backend_api_token = ""  # Optional: Bearer token for API auth

[log]
level = "info"
```

### 3. First-Time Authentication

The first time you run the ingester, you'll need to authenticate:

```bash
cd apps/telegram-ingester
CONFIG_PATH=Config.local.toml cargo run
```

You'll be prompted to enter the verification code sent to your phone.

### 4. Running the Ingester

Once authenticated, the ingester will run automatically:

```bash
# Development
CONFIG_PATH=Config.local.toml cargo run

# Production (with Docker)
docker run -v $(pwd)/Config.local.toml:/app/Config.toml warda/telegram-ingester
```

## Architecture

### Components

1. **TelegramClient**: Handles Telegram API communication
2. **MessageProcessor**: Processes and batches messages
3. **ApiClient**: Communicates with backend API
4. **Config**: Configuration management
5. **Error/Retry**: Error handling and retry logic

### Data Flow

```
Telegram Channels → TelegramClient → MessageProcessor → ApiClient → Backend API → PostgreSQL
```

### Message Processing

1. **Real-time Monitoring**: Continuously monitors configured channels
2. **Data Conversion**: Converts Telegram messages to shared DTOs
3. **Batch Processing**: Groups messages for efficient API calls
4. **User/Channel Management**: Ensures users and channels exist in backend
5. **Error Recovery**: Retries failed operations with exponential backoff

## Database Schema

The ingester works with these database tables:

- `telegram_users`: User information and metadata
- `telegram_channels`: Channel information and statistics  
- `telegram_messages`: Message content and relationships

See `apps/migrate/migrations/002_create_telegram_schema.sql` for the complete schema.

## API Endpoints

The ingester communicates with these backend endpoints:

- `POST /api/telegram/users` - Create/update users
- `POST /api/telegram/channels` - Create/update channels
- `POST /api/telegram/messages/batch` - Batch create messages
- `POST /api/telegram/ingester/health` - Health check

## Configuration Options

### Telegram Settings

- `api_id`: Telegram API ID from my.telegram.org
- `api_hash`: Telegram API hash
- `phone_number`: Your phone number for authentication
- `session_file`: File to store authentication session
- `channels`: List of channel usernames to monitor
- `max_messages_per_batch`: Batch size for API calls

### Backend Settings

- `backend_api_url`: URL of the Warda backend API
- `backend_api_token`: Optional authentication token

### Logging

- `level`: Log level (trace, debug, info, warn, error)

## Deployment

### Docker

Build and run with Docker:

```bash
# Build
make build-multiarch

# Run
docker run -d \
  --name telegram-ingester \
  -v $(pwd)/Config.local.toml:/app/Config.toml \
  warda/telegram-ingester:latest
```

### Kubernetes

The ingester can be deployed to Kubernetes alongside the main Warda application. Add it to your Helm values:

```yaml
telegramIngester:
  enabled: true
  config:
    telegram:
      apiId: "********"
      apiHash: "your_api_hash"
      phoneNumber: "+**********"
      channels:
        - "example_channel"
```

## Monitoring

### Health Checks

The ingester sends periodic health checks to the backend API with:

- Connection status
- Connected channels
- Messages processed count
- Last activity timestamp
- Uptime

### Logs

Monitor the ingester through structured logs:

```bash
# View logs
docker logs telegram-ingester

# Follow logs
docker logs -f telegram-ingester
```

## Troubleshooting

### Authentication Issues

- Ensure your API credentials are correct
- Check that your phone number is in international format
- Verify the verification code is entered correctly

### Connection Issues

- Check network connectivity to Telegram servers
- Verify backend API is accessible
- Check firewall settings

### Channel Access

- Ensure you have access to the channels you want to monitor
- Some channels may require manual joining first
- Private channels need invitation

## Development

### Building

```bash
# Build the ingester
cargo build --bin telegram-ingester

# Run tests
cargo test

# Run with debug logging
RUST_LOG=debug cargo run
```

### Adding Features

The ingester is designed to be extensible:

1. Add new message types in `shared/src/dto.rs`
2. Extend processing logic in `message_processor.rs`
3. Add new API endpoints in backend
4. Update database schema as needed

## Security

- Store API credentials securely
- Use environment variables for sensitive data
- Rotate API tokens regularly
- Monitor for unauthorized access
- Keep session files secure
