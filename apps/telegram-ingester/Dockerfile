# Build stage
FROM rust:1.88.0-slim-bookworm as builder

# Install system dependencies
RUN apt-get update && apt-get install -y pkg-config libssl-dev && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy workspace files
COPY Cargo.toml Cargo.lock ./
COPY crates/ ./crates/
COPY apps/telegram-ingester/ ./apps/telegram-ingester/

# Copy other workspace member Cargo.toml files (required by workspace)
COPY apps/backend/Cargo.toml ./apps/backend/
COPY apps/frontend/Cargo.toml ./apps/frontend/
COPY apps/migrate/Cargo.toml ./apps/migrate/

# Create dummy source files for other workspace members
RUN mkdir -p apps/backend/src apps/frontend/src apps/migrate/src && \
    echo "fn main() {}" > apps/backend/src/main.rs && \
    echo "fn main() {}" > apps/frontend/src/main.rs && \
    echo "pub fn hello() {}" > apps/frontend/src/lib.rs && \
    echo "fn main() {}" > apps/migrate/src/main.rs

# Build the application
RUN cargo build --release --bin telegram-ingester

# Runtime stage
FROM debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN useradd -r -s /bin/false appuser

# Create app directory
WORKDIR /app

# Copy the binary
COPY --from=builder /app/target/release/telegram-ingester /app/telegram-ingester

# Copy configuration template
COPY apps/telegram-ingester/Config.toml /app/Config.toml.template

# Change ownership
RUN chown -R appuser:appuser /app

# Switch to app user
USER appuser

# Expose any ports if needed (none for this ingester)

# Set environment variables
ENV RUST_LOG=info
ENV CONFIG_PATH=/app/Config.toml

# Run the application
CMD ["./telegram-ingester"]
