# Values override for home lab deployment
# This file is used by GitHub Actions for deployment
# Secrets are injected via --set flags from GitHub Secrets

global:
  # Home lab cluster configuration
  clusterHost: "*************"

# Enable Telegram Ingester for home lab only
# Secrets will be overridden by GitHub Actions deployment
telegramIngester:
  enabled: false  # Will be set to true by GitHub Actions

  config:
    # Default configuration - channels will be overridden by GitHub Secrets
    channels: []
    maxMessagesPerBatch: 100
    logLevel: "info"
    rustLog: "telegram_ingester=info,grammers_client=debug"

  # Placeholder secrets - will be overridden by GitHub Actions
  secrets:
    apiId: ""
    apiHash: ""
    phoneNumber: ""
    backendApiToken: ""

  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 100m
      memory: 128Mi

  persistence:
    size: 1Gi

# All other values will inherit from values.yaml
