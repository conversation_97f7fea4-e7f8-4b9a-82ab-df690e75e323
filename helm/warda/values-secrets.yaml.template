# Telegram Ingester Secrets Template
# Copy this file to values-secrets.yaml and fill in your actual values
# DO NOT COMMIT values-secrets.yaml TO GIT

telegramIngester:
  secrets:
    # Get these from https://my.telegram.org/apps
    apiId: "YOUR_TELEGRAM_API_ID"
    apiHash: "YOUR_TELEGRAM_API_HASH"
    phoneNumber: "YOUR_PHONE_NUMBER"  # Format: "+1234567890"
    
    # Optional: Bearer token for backend API authentication
    backendApiToken: ""
  
  config:
    # List of Telegram channels to monitor (usernames without @)
    channels:
      - "example_channel"
      - "another_channel"

# Instructions:
# 1. Copy this file: cp values-secrets.yaml.template values-secrets.yaml
# 2. Edit values-secrets.yaml with your actual Telegram credentials
# 3. Deploy with: helm upgrade --install warda . -f values.yaml -f values-homelab.yaml -f values-secrets.yaml
