# Values override for remote cluster deployment
# Use this file when deploying to your remote k3s cluster at *************

global:
  # Set your cluster IP here - CHAN<PERSON> THIS TO YOUR ACTUAL CLUSTER IP
  clusterHost: "*************"

  # For remote deployment, you'll need to push images to a registry
  # accessible from your remote cluster. Options:
  # 1. Use a public registry like ghcr.io, docker.io, etc.
  # 2. Set up a private registry accessible from your remote cluster
  # 3. Use the multi-arch build and push to your registry
  image:
    registry: "ghcr.io/yourusername"  # Change this to your registry
    repository: warda
    tag: latest
    pullPolicy: Always

  telegramIngester:
    enabled: true

# All other values will inherit from values.yaml
# The Keycloak URLs and redirect URIs will automatically use *************

# IMPORTANT: This will configure:
# - Keycloak admin console: http://*************:30092/admin/
# - Frontend Keycloak URL: http://*************:30092
# - Keycloak redirect URIs: http://*************:30090/*

# To deploy to your remote cluster:
# 1. First, push your images to a public registry:
#    make build-multiarch
# 2. Then deploy with this config:
#    make helm-deploy-remote
# OR use the direct command:
#    helm upgrade --install warda helm/warda -f helm/warda/values.yaml -f helm/warda/values-remote.yaml
