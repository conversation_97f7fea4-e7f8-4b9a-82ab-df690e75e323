{{- if .Values.telegramIngester.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: telegram-ingester
  namespace: {{ .Release.Namespace | default "default" }}
  labels:
    app: telegram-ingester
    chart: {{ include "warda.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  replicas: 1  # Only run one instance to avoid conflicts
  selector:
    matchLabels:
      app: telegram-ingester
      release: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app: telegram-ingester
        release: {{ .Release.Name }}
    spec:
      containers:
      - name: telegram-ingester
        image: "{{ .Values.global.image.registry }}/{{ .Values.global.image.repository }}/telegram-ingester:{{ .Values.global.image.tag }}"
        imagePullPolicy: {{ .Values.global.image.pullPolicy }}
        
        env:
        # Override config with secrets
        - name: TELEGRAM_API_ID
          valueFrom:
            secretKeyRef:
              name: telegram-ingester-secret
              key: api-id
        - name: TELEGRAM_API_HASH
          valueFrom:
            secretKeyRef:
              name: telegram-ingester-secret
              key: api-hash
        - name: TELEGRAM_PHONE_NUMBER
          valueFrom:
            secretKeyRef:
              name: telegram-ingester-secret
              key: phone-number
        {{- if .Values.telegramIngester.secrets.backendApiToken }}
        - name: BACKEND_API_TOKEN
          valueFrom:
            secretKeyRef:
              name: telegram-ingester-secret
              key: backend-api-token
        {{- end }}
        {{- if .Values.telegramIngester.secrets.verificationCode }}
        - name: TELEGRAM_VERIFICATION_CODE
          valueFrom:
            secretKeyRef:
              name: telegram-ingester-secret
              key: verification-code
        {{- end }}
        
        # Configuration file path
        - name: CONFIG_PATH
          value: "/config/Config.toml"
        
        # Logging
        - name: RUST_LOG
          value: "{{ .Values.telegramIngester.config.rustLog | default "telegram_ingester=info" }}"
        
        volumeMounts:
        - name: config
          mountPath: /config
          readOnly: true
        - name: data
          mountPath: /data
        - name: session
          mountPath: /data/session.dat
          subPath: session.dat
          readOnly: true
        
        resources:
          {{- toYaml .Values.telegramIngester.resources | nindent 10 }}
        
        # Health check (optional)
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - "pgrep telegram-ingester"
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - "pgrep telegram-ingester"
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      
      volumes:
      - name: config
        configMap:
          name: telegram-ingester-config
      - name: data
        persistentVolumeClaim:
          claimName: telegram-ingester-data
      - name: session
        secret:
          name: telegram-session
          optional: true
      
      restartPolicy: Always
{{- end }}
