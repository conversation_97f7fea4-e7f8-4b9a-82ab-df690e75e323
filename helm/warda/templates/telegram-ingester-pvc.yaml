{{- if .Values.telegramIngester.enabled }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: telegram-ingester-data
  namespace: {{ .Release.Namespace | default "default" }}
  labels:
    app: telegram-ingester
    chart: {{ include "warda.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: {{ .Values.telegramIngester.persistence.size | default "1Gi" }}
  {{- if .Values.telegramIngester.persistence.storageClass }}
  storageClassName: {{ .Values.telegramIngester.persistence.storageClass }}
  {{- end }}
{{- end }}
