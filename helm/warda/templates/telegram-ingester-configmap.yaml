{{- if .Values.telegramIngester.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: telegram-ingester-config
  namespace: {{ .Release.Namespace | default "default" }}
data:
  Config.toml: |
    [telegram]
    # API credentials will be injected from secrets
    api_id = 0  # Will be overridden by environment variable
    api_hash = ""  # Will be overridden by environment variable
    phone_number = ""  # Will be overridden by environment variable
    
    # Session file to store authentication data
    session_file = "/data/session.dat"
    
    # List of channels to monitor (usernames without @)
    channels = [
      {{- range .Values.telegramIngester.config.channels }}
      "{{ . }}",
      {{- end }}
    ]
    
    # Maximum number of messages to send in a single batch
    max_messages_per_batch = {{ .Values.telegramIngester.config.maxMessagesPerBatch | default 100 }}
    
    # Backend API configuration
    backend_api_url = "http://{{ include "warda.backend.fullname" . }}:{{ .Values.backend.service.port | default 3000 }}"
    backend_api_token = ""  # Will be overridden by environment variable if set
    
    [log]
    level = "{{ .Values.telegramIngester.config.logLevel | default "info" }}"
{{- end }}
