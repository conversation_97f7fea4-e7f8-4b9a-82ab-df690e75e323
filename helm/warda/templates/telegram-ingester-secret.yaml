{{- if .Values.telegramIngester.enabled }}
apiVersion: v1
kind: Secret
metadata:
  name: telegram-ingester-secret
  namespace: {{ .Release.Namespace | default "default" }}
type: Opaque
data:
  # Telegram API credentials (base64 encoded)
  # Get these from https://my.telegram.org/apps
  api-id: {{ .Values.telegramIngester.secrets.apiId | b64enc | quote }}
  api-hash: {{ .Values.telegramIngester.secrets.apiHash | b64enc | quote }}
  phone-number: {{ .Values.telegramIngester.secrets.phoneNumber | b64enc | quote }}
  
  # Optional: Backend API authentication token
  {{- if .Values.telegramIngester.secrets.backendApiToken }}
  backend-api-token: {{ .Values.telegramIngester.secrets.backendApiToken | b64enc | quote }}
  {{- end }}

  # Optional: Telegram verification code (for non-interactive authentication)
  {{- if .Values.telegramIngester.secrets.verificationCode }}
  verification-code: {{ .Values.telegramIngester.secrets.verificationCode | b64enc | quote }}
  {{- end }}
{{- end }}
